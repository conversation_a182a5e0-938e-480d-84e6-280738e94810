<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\AddCartItemRequest;
use App\Http\Resources\Cart\Cart;
use App\Models\Clinic;
use App\Modules\Cart\Services\CartService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class CartController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        private readonly CartService $cartService
    ) {}

    /**
     * Get the clinic's cart.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cartService->getCart($clinic);

        return Cart::make($cart)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }

    /**
     * Add/Delete item/s in the clinic's cart.
     */
    public function create(AddCartItemRequest $request, Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $items = $request->validated('items');
        $cart = $this->cartService->addItems($clinic, $items);

        return Cart::make($cart)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }

    /**
     * Get promotion eligibility for the clinic's cart.
     */
    public function promotions(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $eligibilityResult = $this->cartService->getPromotions($clinic);

        return response()->json($eligibilityResult);
    }

    /**
     * Clean the clinic's cart.
     */
    public function destroy(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cartService->cleanCart($clinic);

        return Cart::make($cart)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
