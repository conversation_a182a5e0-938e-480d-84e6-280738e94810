<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ClinicBudgetType;
use App\Enums\ExpenseCategory;
use App\Enums\TimePeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

final class BudgetSummary
{
    /**
     * The budget metrics.
     *
     * @var Collection<int, BudgetMetric>
     */
    public readonly Collection $metrics;

    /**
     * The clinic's budget type, if any.
     */
    public readonly ?ClinicBudgetType $type;

    /**
     * The clinic's budget settings, if any.
     */
    private readonly ?ClinicBudgetSettings $settings;

    /**
     * Create a new budget summary instance
     */
    public function __construct(private readonly Clinic $clinic)
    {
        $this->settings = $this->clinic->budgetSettings;

        $this->type = $this->settings?->type;

        $this->metrics = collect(ExpenseCategory::cases())
            ->flatMap(function (ExpenseCategory $category) {
                return collect(TimePeriod::cases())
                    ->map(function (TimePeriod $period) use ($category) {
                        return new BudgetMetric(
                            $category,
                            $period,
                            $this->getSpent($period, $category),
                            $this->getSpentPercentage($period, $category),
                            $this->getTarget($period, $category),
                            $this->getTargetPercentage($category),
                        );
                    });
            });
    }

    /**
     * Clear budget summary cache for a clinic.
     */
    public static function clearCache(Clinic $clinic): void
    {
        $cacheKeys = [
            "clinic_{$clinic->id}_spent_WTD_COGS",
            "clinic_{$clinic->id}_spent_MTD_COGS",
            "clinic_{$clinic->id}_spent_WTD_GA",
            "clinic_{$clinic->id}_spent_MTD_GA",
        ];

        foreach ($cacheKeys as $key) {
            cache()->forget($key);
        }
    }

    /**
     * Get the spent amount based on the period and category.
     */
    private function getSpent(TimePeriod $period, ExpenseCategory $category): int
    {
        $cacheKey = "clinic_{$this->clinic->id}_spent_{$period->value}_{$category->value}";

        return (int) cache()->remember($cacheKey, now()->addMinutes(5), function () use ($period, $category) {
            $startDate = match ($period) {
                TimePeriod::WeekToDate => now()->startOfWeek(),
                TimePeriod::MonthToDate => now()->startOfMonth(),
            };

            $externalField = mb_strtolower(match ($period) {
                TimePeriod::WeekToDate => "external_weekly_{$category->value}",
                TimePeriod::MonthToDate => "external_monthly_{$category->value}",
            });

            $total = $this->settings?->include_external_data ? $this->settings->{$externalField} : 0;

            $total += $this->clinic
                ->cart()
                ->firstOrCreate()
                ->items()
                ->whereHas('productOffer.vendor', fn (Builder $query) => $query->where('expense_category', $category))
                ->sum('subtotal');

            $total += $this->clinic
                ->orders()
                ->where('created_at', '>=', $startDate)
                ->whereHas('items.productOffer.vendor', fn (Builder $query) => $query->where('expense_category', $category))
                ->withSum(['items as total_price_sum' => fn (Builder $query) => $query
                    ->whereHas('productOffer.vendor', fn (Builder $query) => $query->where('expense_category', $category)),
                ], 'total_price')
                ->get()
                ->sum('total_price_sum');

            return $total;
        });
    }

    /**
     * Get the spent percentage based on the period and category.
     */
    private function getSpentPercentage(TimePeriod $period, ExpenseCategory $category): float
    {
        if ($this->getSpent($period, $category) === 0 || $this->getTarget($period, $category) === 0) {
            return 0;
        }

        return (float) ($this->getSpent($period, $category) / $this->getTarget($period, $category)) * 100;
    }

    /**
     * Get target amount based on budget type, period and category.
     */
    private function getTarget(TimePeriod $period, ExpenseCategory $category): int
    {
        $staticField = mb_strtolower(match ($period) {
            TimePeriod::WeekToDate => "weekly_{$category->value}",
            TimePeriod::MonthToDate => "monthly_{$category->value}",
        });

        $percentageField = mb_strtolower("target_{$category->value}_percent");

        $salesField = match ($period) {
            TimePeriod::WeekToDate => 'avg_two_weeks_sales',
            TimePeriod::MonthToDate => 'month_to_date_sales',
        };

        return match ($this->type) {
            ClinicBudgetType::Static => $this->settings->{$staticField},
            ClinicBudgetType::Dynamic => (int) ($this->settings->{$percentageField} * $this->settings->{$salesField}),
            default => 0,
        };
    }

    /**
     * Get the target percentage based on budget type, period and category.
     */
    private function getTargetPercentage(ExpenseCategory $category): float
    {
        $percentageField = mb_strtolower("target_{$category->value}_percent");

        return match ($this->type) {
            ClinicBudgetType::Dynamic => $this->settings->{$percentageField} * 100,
            default => 0,
        };
    }
}
