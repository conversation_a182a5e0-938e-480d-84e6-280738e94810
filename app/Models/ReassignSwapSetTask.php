<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ReassignSwapSetTaskObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

#[ObservedBy(ReassignSwapSetTaskObserver::class)]
final class ReassignSwapSetTask extends Model
{
    use HasUuids;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'total_rows_count' => 'integer',
            'processsed_rows_count' => 'integer',
            'skipped_or_failed_rows_count' => 'integer',
            'started_at' => 'datetime',
            'finished_at' => 'datetime',
        ];
    }
}
