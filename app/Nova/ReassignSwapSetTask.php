<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\File;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class ReassignSwapSetTask extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ReassignSwapSetTask>
     */
    public static $model = \App\Models\ReassignSwapSetTask::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            File::make('CSV File', 'file_path')
                ->disk('local')
                ->path('reassign-swap-sets')
                ->acceptedTypes('.csv')
                ->rules('required')
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->hideFromIndex(),

            Number::make('Total Rows', 'total_rows_count')
                ->hideFromIndex()
                ->readonly(),

            Number::make('Processed Rows', 'processed_rows_count')
                ->hideFromIndex()
                ->readonly(),

            Number::make('Skipped Or Failed Rows', 'skipped_or_failed_rows_count')
                ->hideFromIndex()
                ->readonly(),

            DateTime::make('Started At', 'started_at')
                ->hideFromIndex()
                ->readonly(),

            DateTime::make('Finished At', 'finished_at')
                ->hideFromIndex()
                ->readonly(),
        ];
    }
}
