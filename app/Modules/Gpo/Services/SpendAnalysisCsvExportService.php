<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Modules\Gpo\Http\Requests\SpendAnalysisRequest;
use Illuminate\Database\Eloquent\Collection;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class SpendAnalysisCsvExportService
{
    public function __construct(
        private readonly SpendAnalysisService $spendAnalysisService
    ) {}

    public function exportToCsv(string $gpoAccountId, SpendAnalysisRequest $request): StreamedResponse
    {
        $fileName = $this->generateFileName();

        $callback = function () use ($gpoAccountId, $request) {
            $file = fopen('php://output', 'w');

            $this->writeCsvHeaders($file);
            $this->writeCsvData($file, $gpoAccountId, $request);

            fclose($file);
        };

        return response()->streamDownload($callback, $fileName, [
            'Content-Type' => 'text/csv',
        ]);
    }

    private function generateFileName(): string
    {
        return 'gpo_spend_analysis_'.now()->format('Y-m-d_H-i-s').'.csv';
    }

    private function writeCsvHeaders($file): void
    {
        fputcsv($file, [
            'ID',
            'Clinic Name',
            'Member Since',
            'Status',
            'Type of Practice',
            'Fulltime DVM',
            'Total Exam Rooms',
            'Total Spend',
            'Total Spend YOY %',
            'Rebates Earned',
            'Rebates Earned YOY %',
            'Preferred Share Amount',
            'Preferred Share %',
            'Preferred Share YOY %',
            'Preferred Vendor Spend Share Amount',
            'Preferred Vendor Spend Share Total',
            'Preferred Vendor Spend Share %',
            'Preferred Vendor Spend Share YOY %',
            'Annual Budget',
            'Previous Year Spend',
            'Preferred Vendor %',
            'Non-Preferred Vendor %',
            'GPO Participation Rate %',
            'GPO Participation Rate YOY %',
            'Total Orders',
            'Active Users',
        ]);
    }

    private function writeCsvData($file, string $gpoAccountId, SpendAnalysisRequest $request): void
    {
        // Process data in chunks to avoid memory issues
        $this->spendAnalysisService
            ->getClinicsForExport($gpoAccountId, $request)
            ->chunk(1000)
            ->each(function (Collection $clinics) use ($file) {
                foreach ($clinics as $clinic) {
                    $data = $this->spendAnalysisService->formatClinicData($clinic);
                    $this->writeCsvRow($file, $data);
                }
            });
    }

    private function writeCsvRow($file, array $data): void
    {
        fputcsv($file, [
            $data['id'],
            $data['name'],
            $data['member_since'],
            $data['status'],
            implode(', ', $data['practice_types'] ?? []),
            $data['fulltime_dvm'],
            $data['total_exam_rooms'],
            $data['total_spend']['amount'] ?? '0.00',
            $data['total_spend']['yoy_percentage'].'%',
            $data['rebates_earned']['amount'] ?? '0.00',
            $data['rebates_earned']['yoy_percentage'].'%',
            $data['preferred_share']['amount'] ?? '0.00',
            $data['preferred_share']['percentage'].'%',
            $data['preferred_share']['yoy_percentage'].'%',
            $data['preferred_vendor_spend_share']['amount'] ?? '0.00',
            $data['preferred_vendor_spend_share']['total'] ?? '0.00',
            $data['preferred_vendor_spend_share']['percentage'].'%',
            $data['preferred_vendor_spend_share']['yoy_percentage'].'%',
            $data['spend']['annual_budget'] ?? '0.00',
            $data['spend']['previous_year_spend'] ?? '0.00',
            $data['spend']['preferred_vendor_percentage'].'%',
            $data['spend']['non_preferred_vendor_percentage'].'%',
            $data['market_share_analysis']['gpo_vendor_participation_rate']['percentage'].'%',
            $data['market_share_analysis']['gpo_vendor_participation_rate']['yoy_percentage'].'%',
            $data['total_orders'],
            $data['active_users'],
        ]);
    }
}
