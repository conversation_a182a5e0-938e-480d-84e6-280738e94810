<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Models\Clinic;
use App\Models\OrderItem;
use App\Models\User;
use App\Modules\Clinic\Enums\PracticeType;
use App\Modules\Gpo\Http\Requests\SpendAnalysisRequest;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Pagination\LengthAwarePaginator as PaginationLengthAwarePaginator;
use Illuminate\Support\Facades\DB;

final class SpendAnalysisService
{
    public function getClinicsWithAnalytics(string $gpoAccountId, SpendAnalysisRequest $request): LengthAwarePaginator
    {
        $query = $this->buildBaseQuery($gpoAccountId, $request);
        $this->applyBasicFilters($query, $request);
        $this->applyOrderBy($query, $request);

        // Get all results and apply advanced filters in PHP to avoid PostgreSQL GROUP BY issues
        $allResults = $query->get();
        $filteredResults = $this->applyAdvancedFilters($allResults, $request);

        // Manual pagination
        $perPage = $request->get('per_page', 25);
        $page = $request->get('page', 1);
        $total = $filteredResults->count();
        $items = $filteredResults->forPage($page, $perPage);

        return new PaginationLengthAwarePaginator(
            $items->values()->map(fn ($clinic) => $this->formatClinicData($clinic, $request)),
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'pageName' => 'page']
        );
    }

    public function getClinicsForExport(string $gpoAccountId, SpendAnalysisRequest $request): Collection
    {
        $query = $this->buildBaseQuery($gpoAccountId, $request);
        $this->applyBasicFilters($query, $request);
        $this->applyOrderBy($query, $request);

        $allResults = $query->get();
        $filteredResults = $this->applyAdvancedFilters($allResults, $request);

        return $filteredResults->map(fn ($clinic) => $this->formatClinicData($clinic, $request));
    }

    public function formatClinicData($clinic, ?SpendAnalysisRequest $request = null): array
    {
        $totalSpend = (float) ($clinic->total_spend ?? 0);
        $preferredSpend = (float) ($clinic->preferred_vendor_spend ?? 0);
        $preferredPercent = $totalSpend > 0 ? ($preferredSpend / $totalSpend) * 100 : 0;
        $nonPreferredSpend = $totalSpend - $preferredSpend;
        $nonPreferredPercent = $totalSpend > 0 ? ($nonPreferredSpend / $totalSpend) * 100 : 0;

        // Get previous year data for YOY calculations
        $previousYearData = $this->getPreviousYearData($clinic);
        $annualBudget = $this->getAnnualBudget($clinic);
        $rebatesEarned = $this->getRebatesEarned($clinic);
        $gpoParticipationRate = $this->calculateGpoParticipationRate($clinic);

        return [
            'id' => $clinic->id,
            'name' => $clinic->name,
            'member_since' => $clinic->created_at?->format('Y-m-d'),
            'status' => ($clinic->total_orders ?? 0) > 0 ? 'ACTIVE' : 'INACTIVE',
            'practice_types' => $clinic->practice_types?->map(fn ($type) => $type->value)->toArray() ?? [],
            'fulltime_dvm' => $clinic->fulltime_dvm_count ?? 0,
            'total_exam_rooms' => $clinic->exam_rooms_count ?? 0,

            // Total Spend with YOY
            'total_spend' => [
                'amount' => number_format($totalSpend, 2),
                'yoy_percentage' => $this->calculateYoyPercentage($totalSpend, $previousYearData['total_spend']),
            ],

            // Rebates Earned with YOY
            'rebates_earned' => [
                'amount' => number_format($rebatesEarned, 2),
                'yoy_percentage' => $this->calculateYoyPercentage($rebatesEarned, $previousYearData['rebates_earned']),
            ],

            // Preferred Share with YOY
            'preferred_share' => [
                'amount' => number_format($preferredSpend, 2),
                'percentage' => round($preferredPercent, 1),
                'yoy_percentage' => $this->calculateYoyPercentage($preferredPercent, $previousYearData['preferred_percent']),
            ],

            // Preferred Vendor Spend Share with YOY
            'preferred_vendor_spend_share' => [
                'amount' => number_format($preferredSpend, 2),
                'total' => number_format($totalSpend, 2),
                'percentage' => round($preferredPercent, 1),
                'yoy_percentage' => $this->calculateYoyPercentage($preferredSpend, $previousYearData['preferred_spend']),
            ],

            // Spend Details
            'spend' => [
                'total_spend' => number_format($totalSpend, 2),
                'annual_budget' => number_format($annualBudget['amount'], 2),
                'previous_year_spend' => number_format($previousYearData['total_spend'], 2),
                'preferred_vendor_percentage' => round($preferredPercent, 1),
                'non_preferred_vendor_percentage' => round($nonPreferredPercent, 1),
            ],

            // Market Share Analysis with YOY
            'market_share_analysis' => [
                'gpo_vendor_participation_rate' => [
                    'percentage' => round($gpoParticipationRate, 1),
                    'yoy_percentage' => $this->calculateYoyPercentage($gpoParticipationRate, $previousYearData['gpo_participation_rate']),
                    'description' => round($gpoParticipationRate, 1).'% (5 out of 10)', // @TODO: Replace hardcoded "5 out of 10" with actual values
                ],
                'distributors' => $this->getDistributorBreakdown($clinic, $request),
                'vendors' => $this->getVendorBreakdown($clinic, $request),
                'product_categories' => $this->getProductCategoryBreakdown($clinic, $request),
            ],

            // Activity Information
            'total_orders' => (int) ($clinic->total_orders ?? 0),
            'active_users' => (int) ($clinic->active_users ?? 0),
            'notifications' => 0, // @TODO: Implement actual notification count when notification logic is finalized
            'is_inactive' => ($clinic->total_orders ?? 0) === 0,
            'last_order_date' => $clinic->last_order_date ?? null,

            // Legacy fields for backward compatibility
            'preferred_vendor_percent' => round($preferredPercent, 2),
        ];
    }

    private function buildBaseQuery(string $gpoAccountId, SpendAnalysisRequest $request): Builder
    {
        $dateFrom = $this->getDateFrom($request);
        $dateTo = $this->getDateTo($request);

        return Clinic::query()
            ->withCount([
                'orders as total_orders' => function ($query) use ($dateFrom, $dateTo) {
                    $query->whereBetween('created_at', [$dateFrom, $dateTo])
                        ->whereNull('deleted_at');
                },
            ])
            ->addSelect([
                'total_spend' => $this->buildTotalSpendSubquery($dateFrom, $dateTo),
                'preferred_vendor_spend' => $this->buildPreferredVendorSpendSubquery($gpoAccountId, $dateFrom, $dateTo),
                'active_users' => $this->buildActiveUsersSubquery($dateFrom, $dateTo),
                'last_order_date' => $this->buildLastOrderDateSubquery(),
            ])
            ->whereHas('account', function ($query) use ($gpoAccountId) {
                $query->where('gpo_account_id', $gpoAccountId)
                    ->where('type', 'App\\Modules\\Account\\Models\\ClinicAccount');
            });
    }

    private function buildTotalSpendSubquery(string $dateFrom, string $dateTo): Builder
    {
        return OrderItem::selectRaw('COALESCE(SUM(price * quantity), 0)')
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->whereColumn('orders.clinic_id', 'clinics.id')
            ->whereBetween('orders.created_at', [$dateFrom, $dateTo])
            ->whereNull('order_items.deleted_at');
    }

    private function buildPreferredVendorSpendSubquery(string $gpoAccountId, string $dateFrom, string $dateTo): Builder
    {
        return OrderItem::selectRaw('COALESCE(SUM(CASE WHEN grv.vendor_id IS NOT NULL THEN order_items.price * order_items.quantity ELSE 0 END), 0)')
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->join('sub_orders', 'sub_orders.order_id', '=', 'orders.id')
            ->leftJoin('gpo_recommended_vendors as grv', function ($join) use ($gpoAccountId) {
                $join->on('grv.vendor_id', '=', 'sub_orders.vendor_id')
                    ->where('grv.gpo_account_id', '=', $gpoAccountId);
            })
            ->whereColumn('orders.clinic_id', 'clinics.id')
            ->whereBetween('orders.created_at', [$dateFrom, $dateTo])
            ->whereNull('order_items.deleted_at');
    }

    private function buildActiveUsersSubquery(string $dateFrom, string $dateTo): Builder
    {
        return User::selectRaw('COALESCE(COUNT(DISTINCT users.id), 0)')
            ->join('clinic_user', 'users.id', '=', 'clinic_user.user_id')
            ->whereColumn('clinic_user.clinic_id', 'clinics.id')
            ->whereBetween('users.created_at', [$dateFrom, $dateTo])
            ->whereNull('users.deleted_at');
    }

    private function buildLastOrderDateSubquery(): QueryBuilder
    {
        return DB::table('orders')
            ->selectRaw('MAX(created_at)')
            ->whereColumn('clinic_id', 'clinics.id')
            ->whereNull('deleted_at');
    }

    private function applyBasicFilters(Builder $query, SpendAnalysisRequest $request): void
    {
        $this->applyClinicInfoFilters($query, $request);
        $this->applyQuarterFilter($query, $request);
    }

    private function applyAdvancedFilters(Collection $results, SpendAnalysisRequest $request): Collection
    {
        return $results->filter(function ($clinic) use ($request) {
            // Use raw database values for filtering to avoid formatting issues
            $totalSpend = (float) ($clinic->total_spend ?? 0);
            $preferredSpend = (float) ($clinic->preferred_vendor_spend ?? 0);
            $preferredPercent = $totalSpend > 0 ? ($preferredSpend / $totalSpend) * 100 : 0;
            $totalOrders = (int) ($clinic->total_orders ?? 0);

            // Apply spend filters
            if ($request->filled('min_spend') && $totalSpend < $request->get('min_spend')) {
                return false;
            }
            if ($request->filled('max_spend') && $totalSpend > $request->get('max_spend')) {
                return false;
            }

            // Apply preferred vendor percentage filters
            if ($request->filled('min_preferred_vendor_percent') && $preferredPercent < $request->get('min_preferred_vendor_percent')) {
                return false;
            }
            if ($request->filled('max_preferred_vendor_percent') && $preferredPercent > $request->get('max_preferred_vendor_percent')) {
                return false;
            }

            // Apply order filters
            if ($request->filled('min_orders') && $totalOrders < $request->get('min_orders')) {
                return false;
            }
            if ($request->filled('max_orders') && $totalOrders > $request->get('max_orders')) {
                return false;
            }

            // Apply inactive filter
            if ($request->boolean('inactive_only') && $totalOrders > 0) {
                return false;
            }

            return true;
        });
    }

    private function applyClinicInfoFilters(Builder $query, SpendAnalysisRequest $request): void
    {
        if ($request->filled('fulltime_dvm_min')) {
            $query->where('fulltime_dvm_count', '>=', $request->get('fulltime_dvm_min'));
        }

        if ($request->filled('fulltime_dvm_max')) {
            $query->where('fulltime_dvm_count', '<=', $request->get('fulltime_dvm_max'));
        }

        if ($request->filled('total_exam_rooms_min')) {
            $query->where('exam_rooms_count', '>=', $request->get('total_exam_rooms_min'));
        }

        if ($request->filled('total_exam_rooms_max')) {
            $query->where('exam_rooms_count', '<=', $request->get('total_exam_rooms_max'));
        }

        if ($request->filled('practice_types') && is_array($request->get('practice_types'))) {
            $practiceTypes = collect($request->get('practice_types'))
                ->map(fn ($type) => PracticeType::tryFrom($type))
                ->filter()
                ->toArray();

            if (! empty($practiceTypes)) {
                $query->where(function ($q) use ($practiceTypes) {
                    foreach ($practiceTypes as $type) {
                        $q->orWhereJsonContains('practice_types', $type->value);
                    }
                });
            }
        }
    }

    private function applyQuarterFilter(Builder $query, SpendAnalysisRequest $request): void
    {
        if ($request->filled('quarter') && $request->filled('year')) {
            $year = $request->get('year');
            $quarter = $request->get('quarter');

            $quarterDateRanges = [
                1 => ["{$year}-01-01", "{$year}-03-31"],
                2 => ["{$year}-04-01", "{$year}-06-30"],
                3 => ["{$year}-07-01", "{$year}-09-30"],
                4 => ["{$year}-10-01", "{$year}-12-31"],
            ];

            if (isset($quarterDateRanges[$quarter])) {
                // @TODO: Implement proper quarter filtering using the date ranges
                // Quarter filtering implementation - dates are available for future use
                // Currently using the existing date_from/date_to logic in the request
                $query->having(DB::raw('1'), '=', DB::raw('1')); // Always true condition
            }
        }
    }

    private function applyOrderBy(Builder $query, SpendAnalysisRequest $request): void
    {
        $orderBy = $request->get('order_by', 'name');
        $direction = $request->get('direction', 'asc');

        switch ($orderBy) {
            case 'active_users':
                $query->orderBy('active_users', $direction);
                break;
            case 'inactive_users':
                $query->orderByRaw('CASE WHEN total_orders = 0 THEN name ELSE NULL END '.$direction);
                break;
            case 'notifications':
                // @TODO: Implement actual notification count ordering when notification logic is finalized
                $query->orderByRaw('0 '.$direction);
                break;
            case 'total_spend':
                $dateFrom = $this->getDateFrom($request);
                $dateTo = $this->getDateTo($request);
                $query->orderByRaw('(
                    SELECT COALESCE(SUM(price * quantity), 0) 
                    FROM order_items 
                    INNER JOIN orders ON orders.id = order_items.order_id 
                    WHERE orders.clinic_id = clinics.id 
                    AND orders.created_at BETWEEN ? AND ? 
                    AND order_items.deleted_at IS NULL
                ) '.$direction, [$dateFrom, $dateTo]);
                break;
            case 'preferred_vendor_percent':
                $query->orderByRaw('
                    CASE 
                        WHEN total_spend > 0 
                        THEN (preferred_vendor_spend * 100.0 / total_spend) 
                        ELSE 0 
                    END '.$direction
                );
                break;
            default:
                $query->orderBy('name', $direction);
                break;
        }
    }

    private function getDateFrom(SpendAnalysisRequest $request): string
    {
        return $request->get('date_from', now()->subMonths(3)->format('Y-m-d'));
    }

    private function getDateTo(SpendAnalysisRequest $request): string
    {
        return $request->get('date_to', now()->format('Y-m-d'));
    }

    private function calculateGpoParticipationRate($clinic): float
    {
        // @TODO: Replace placeholder with actual calculation from database
        // Placeholder calculation - should be based on actual vendor participation
        // This would typically involve checking how many GPO preferred vendors the clinic uses
        // vs total available GPO vendors
        return 60.0; // Mock data matching UI
    }

    private function getAnnualBudget($clinic): array
    {
        // @TODO: Replace with actual budget data from clinic records
        // This would typically come from clinic budget settings or estimates
        $budget = 650000.00; // Mock data

        return [
            'amount' => $budget,
        ];
    }

    private function getPreviousYearSpend($clinic): array
    {
        // @TODO: Replace with actual query for previous year's spending
        // This would involve querying orders from previous year
        $previousSpend = 602450.50; // Mock data

        return [
            'amount' => $previousSpend,
        ];
    }

    private function getDistributorBreakdown($clinic, ?SpendAnalysisRequest $request = null): array
    {
        // @TODO: Replace with actual distributor data from database
        // Mock data matching the UI - this would come from actual order data
        $allDistributors = [
            ['name' => 'MWI', 'percentage' => 30],
            ['name' => 'Dist. A', 'percentage' => 25],
            ['name' => 'Dist. B', 'percentage' => 20],
            ['name' => 'Dist. C', 'percentage' => 15],
            ['name' => 'Dist. D', 'percentage' => 10],
        ];

        $limit = $request ? $request->get('distributors_limit', 3) : 3;

        return $this->applyLimitWithOthers($allDistributors, $limit);
    }

    private function getVendorBreakdown($clinic, ?SpendAnalysisRequest $request = null): array
    {
        // @TODO: Replace with actual vendor data from database
        // Mock data matching the UI
        $allVendors = [
            ['name' => 'Zoetis', 'percentage' => 35],
            ['name' => 'Wedgewood', 'percentage' => 20],
            ['name' => 'Vendor A', 'percentage' => 15],
            ['name' => 'Vendor B', 'percentage' => 12],
            ['name' => 'Vendor C', 'percentage' => 10],
            ['name' => 'Vendor D', 'percentage' => 8],
        ];

        $limit = $request ? $request->get('vendors_limit', 3) : 3;

        return $this->applyLimitWithOthers($allVendors, $limit);
    }

    private function getProductCategoryBreakdown($clinic, ?SpendAnalysisRequest $request = null): array
    {
        $categoryVendorLimit = $request ? $request->get('category_vendors_limit', 3) : 3;

        // @TODO: Replace with actual product category data from database
        // Mock data matching the UI - this should be based on actual order data
        return [
            [
                'name' => 'Parasiticides',
                'total_percentage' => 50,
                'vendors' => $this->applyLimitWithOthers([
                    ['name' => 'Zoetis', 'percentage' => 40],
                    ['name' => 'Vendor C', 'percentage' => 25],
                    ['name' => 'Vendor B', 'percentage' => 20],
                    ['name' => 'Vendor E', 'percentage' => 10],
                    ['name' => 'Vendor F', 'percentage' => 5],
                ], $categoryVendorLimit),
            ],
            [
                'name' => 'Vaccines',
                'total_percentage' => 30,
                'vendors' => $this->applyLimitWithOthers([
                    ['name' => 'Zoetis', 'percentage' => 45],
                    ['name' => 'Vendor C', 'percentage' => 25],
                    ['name' => 'Vendor A', 'percentage' => 15],
                    ['name' => 'Vendor B', 'percentage' => 10],
                    ['name' => 'Vendor G', 'percentage' => 5],
                ], $categoryVendorLimit),
            ],
            [
                'name' => 'Diets',
                'total_percentage' => 20,
                'vendors' => $this->applyLimitWithOthers([
                    ['name' => 'Royal Canin', 'percentage' => 50],
                    ['name' => 'Vendor F', 'percentage' => 25],
                    ['name' => 'Vendor D', 'percentage' => 15],
                    ['name' => 'Vendor H', 'percentage' => 7],
                    ['name' => 'Vendor I', 'percentage' => 3],
                ], $categoryVendorLimit),
            ],
        ];
    }

    private function getRebatesEarned($clinic): float
    {
        // @TODO: Replace with actual rebate calculation from order data
        // This would involve calculating actual rebates from orders
        // Mock data for now
        return 75000.00;
    }

    private function getPreviousYearData($clinic): array
    {
        // @TODO: Replace with actual queries for previous year's data
        // This would typically query the database for previous year's data
        // Mock data for now - in production this would involve complex queries
        return [
            'total_spend' => 602450.50,
            'preferred_spend' => 451837.88,
            'preferred_percent' => 75.0,
            'rebates_earned' => 68500.00,
            'gpo_participation_rate' => 58.5,
        ];
    }

    private function calculateYoyPercentage(float $currentValue, float $previousValue): float
    {
        if ($previousValue === 0) {
            return $currentValue > 0 ? 100.0 : 0.0;
        }

        $yoyChange = (($currentValue - $previousValue) / $previousValue) * 100;

        return round($yoyChange, 1);
    }

    private function applyLimitWithOthers(array $items, int $limit): array
    {
        // If we have fewer items than the limit, return all items
        if (count($items) <= $limit) {
            return $items;
        }

        // Take the top items based on the limit
        $topItems = array_slice($items, 0, $limit - 1);

        // Calculate the sum of remaining items for "Others"
        $remainingItems = array_slice($items, $limit - 1);
        $othersPercentage = array_sum(array_column($remainingItems, 'percentage'));

        // Add "Others" entry if there are remaining items
        if ($othersPercentage > 0) {
            $topItems[] = [
                'name' => 'Others',
                'percentage' => round($othersPercentage, 1),
            ];
        }

        return $topItems;
    }
}
