<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Models\Vendor;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class VendorOverviewData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $type,
        public readonly ?string $imageUrl,
        public readonly float $marketSharePercentage,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $totalSpend,
        public readonly float $growthTargetPercentage,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $amountUntilGoal,
        public readonly ?string $notification,
    ) {}

    public static function fromModel(
        Vendor $vendor,
        float $marketSharePercentage,
        int $totalSpend,
        float $growthTargetPercentage,
        int $amountUntilGoal,
        ?string $notification = null
    ): self {
        return new self(
            id: $vendor->id,
            name: $vendor->name,
            type: $vendor->type->value,
            imageUrl: $vendor->image_path ? asset("storage/{$vendor->image_path}") : null,
            marketSharePercentage: $marketSharePercentage,
            totalSpend: Money::ofMinor($totalSpend, 'USD'),
            growthTargetPercentage: $growthTargetPercentage,
            amountUntilGoal: Money::ofMinor($amountUntilGoal, 'USD'),
            notification: $notification,
        );
    }
}
