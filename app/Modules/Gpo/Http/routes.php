<?php

declare(strict_types=1);

use App\Modules\Gpo\Http\Controllers\AuthController;
use App\Modules\Gpo\Http\Controllers\SpendAnalysisController;
use App\Modules\Gpo\Http\Controllers\VendorsOverviewController;
use Illuminate\Support\Facades\Route;

Route::prefix('api/gpo')->group(function () {
    Route::post('login', [AuthController::class, 'login']);

    Route::middleware('auth:gpo')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);

        Route::get('spend-analysis', [SpendAnalysisController::class, 'index']);
        Route::get('spend-analysis/export', [SpendAnalysisController::class, 'export']);
        Route::get('vendors-overview', [VendorsOverviewController::class, 'index']);
        Route::get('vendors-overview/export', [VendorsOverviewController::class, 'export']);
    });
});
