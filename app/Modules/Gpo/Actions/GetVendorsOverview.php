<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Actions;

use App\Models\OrderItem;
use App\Models\Vendor;
use App\Modules\Gpo\Data\VendorOverviewData;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\GpoAccount;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

final readonly class GetVendorsOverview
{
    /**
     * Default performance threshold for growth target percentage.
     * Used when no setting is configured for the GPO account.
     */
    private const DEFAULT_PERFORMANCE_THRESHOLD = 85.0;

    /**
     * Default goal amount for each vendor (in cents).
     * Used when no setting is configured for the GPO account.
     */
    private const DEFAULT_VENDOR_GOAL_AMOUNT = 1000000; // $10,000 in cents

    /**
     * Get vendors overview with calculated metrics for the given date range.
     */
    public function handle(string $gpoAccountId, Carbon $startDate, Carbon $endDate): Collection
    {
        $vendors = Vendor::isEnabled()->get();
        $vendorSpendData = $this->getVendorSpendData($gpoAccountId, $startDate, $endDate);

        if ($vendorSpendData->isEmpty()) {
            return collect();
        }

        $totalSpendAcrossAllVendors = $vendorSpendData->sum('total_spend');

        if ($totalSpendAcrossAllVendors === 0) {
            return collect();
        }

        // Get GPO account settings for this specific account
        $gpoAccount = GpoAccount::findOrFail($gpoAccountId);
        $performanceThreshold = $this->getPerformanceThreshold($gpoAccount);
        $vendorGoals = $this->getVendorGoals($gpoAccount);
        $notificationMessage = $this->getNotificationMessage($gpoAccount);

        return $vendors->map(function (Vendor $vendor) use ($vendorSpendData, $totalSpendAcrossAllVendors, $performanceThreshold, $vendorGoals, $notificationMessage): ?VendorOverviewData {
            $vendorGoalAmount = $this->getVendorGoalAmount($vendor, $vendorGoals);
            $vendorTotalSpend = $vendorSpendData->where('vendor_id', $vendor->id)->first()?->total_spend ?? 0;

            // Filter out vendors that have no spend and no goal amount
            if ($vendorTotalSpend === 0 && $vendorGoalAmount === self::DEFAULT_VENDOR_GOAL_AMOUNT) {
                return null;
            }

            return $this->calculateVendorMetrics($vendor, $vendorTotalSpend, $totalSpendAcrossAllVendors, $performanceThreshold, $vendorGoalAmount, $notificationMessage);
        })
            ->reject(fn (?VendorOverviewData $vendorOverviewData) => $vendorOverviewData === null)
            ->values();
    }

    /**
     * Get all vendor spend data for the given date range.
     */
    private function getVendorSpendData(string $gpoAccountId, Carbon $startDate, Carbon $endDate): Collection
    {
        return Cache::remember('vendor-spend-data-'.$gpoAccountId.'-'.$startDate->toDateString().'-'.$endDate->toDateString(), Carbon::now()->addDay(),
            function () use ($gpoAccountId, $startDate, $endDate) {
                return OrderItem::query()
                    ->select([
                        'product_offers.vendor_id',
                        DB::raw('SUM(order_items.total_price) as total_spend'),
                    ])
                    ->join('orders', 'orders.id', '=', 'order_items.order_id')
                    ->join('clinics', 'clinics.id', '=', 'orders.clinic_id')
                    ->join('accounts', 'accounts.id', '=', 'clinics.clinic_account_id')
                    ->join('product_offers', 'product_offers.id', '=', 'order_items.product_offer_id')
                    ->where('accounts.gpo_account_id', $gpoAccountId)
                    ->whereNull('orders.import_order_history_task_id')
                    ->whereBetween('orders.created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
                    ->groupBy('product_offers.vendor_id')
                    ->get();
            });
    }

    /**
     * Calculate metrics for a specific vendor.
     */
    private function calculateVendorMetrics(
        Vendor $vendor,
        int $vendorTotalSpend,
        int $totalSpendAcrossAllVendors,
        float $performanceThreshold,
        int $vendorGoalAmount,
        ?string $notificationMessage
    ): VendorOverviewData {
        $marketSharePercentage = $this->calculateMarketSharePercentage($vendorTotalSpend, $totalSpendAcrossAllVendors);

        $growthTargetPercentage = $this->calculateGrowthTargetPercentage($vendorTotalSpend, $vendorGoalAmount);

        $amountUntilGoal = $this->calculateAmountUntilGoal($vendorTotalSpend, $vendorGoalAmount);

        $notification = $this->getPerformanceNotification($growthTargetPercentage, $performanceThreshold, $notificationMessage);

        return VendorOverviewData::fromModel(
            vendor: $vendor,
            marketSharePercentage: $marketSharePercentage,
            totalSpend: $vendorTotalSpend,
            growthTargetPercentage: $growthTargetPercentage,
            amountUntilGoal: $amountUntilGoal,
            notification: $notification,
        );
    }

    private function getPerformanceThreshold(GpoAccount $gpoAccount): float
    {
        $setting = $gpoAccount->settings()
            ->where('setting_key', GpoSettingKey::PerformanceThreshold)
            ->first();

        if (! $setting || ! $setting->value['enabled']) {
            return self::DEFAULT_PERFORMANCE_THRESHOLD;
        }

        return $setting->value['threshold'] ?? self::DEFAULT_PERFORMANCE_THRESHOLD;
    }

    private function getNotificationMessage(GpoAccount $gpoAccount): ?string
    {
        $setting = $gpoAccount->settings()
            ->where('setting_key', GpoSettingKey::PerformanceThreshold)
            ->first();

        return $setting?->value['notification_message'] ?? null;
    }

    /**
     * Get vendor goals setting for the GPO account.
     */
    private function getVendorGoals(GpoAccount $gpoAccount): ?array
    {
        $setting = $gpoAccount->settings()
            ->where('setting_key', GpoSettingKey::VendorGoals)
            ->first();

        return $setting?->value['goals'] ?? null;
    }

    /**
     * Get the goal amount for a specific vendor, with fallback to default.
     */
    private function getVendorGoalAmount(Vendor $vendor, ?array $vendorGoals): int
    {
        if (! $vendorGoals) {
            return self::DEFAULT_VENDOR_GOAL_AMOUNT;
        }

        foreach ($vendorGoals as $goal) {
            if ($goal['enabled'] && $goal['vendor_id'] === $vendor->id) {
                return $goal['goal_amount'];
            }
        }

        return self::DEFAULT_VENDOR_GOAL_AMOUNT;
    }

    private function calculateMarketSharePercentage(int $vendorTotalSpend, int $totalSpendAcrossAllVendors): float
    {
        if ($totalSpendAcrossAllVendors === 0) {
            return 0.0;
        }

        $percentage = ($vendorTotalSpend / $totalSpendAcrossAllVendors) * 100;

        return round($percentage, 2);
    }

    private function calculateGrowthTargetPercentage(int $vendorTotalSpend, int $vendorGoalAmount): float
    {
        if ($vendorGoalAmount === 0) {
            return 0.0;
        }

        $percentage = ($vendorTotalSpend / $vendorGoalAmount) * 100;

        return round($percentage, 2);
    }

    private function calculateAmountUntilGoal(int $vendorTotalSpend, int $vendorGoalAmount): int
    {
        $amountUntilGoal = $vendorGoalAmount - $vendorTotalSpend;

        return $amountUntilGoal;
    }

    private function getPerformanceNotification(float $growthTargetPercentage, float $performanceThreshold, ?string $notificationMessage): ?string
    {
        if ($growthTargetPercentage < $performanceThreshold) {
            if ($notificationMessage) {
                return $notificationMessage;
            }

            return 'Performance lower than expected – consider action';
        }

        return null;
    }
}
