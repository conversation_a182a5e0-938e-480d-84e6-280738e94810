<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\SubOrder;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Order\Models\ExternalOrder;
use Brick\Money\Money;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $productOffer = ProductOffer::factory()->create();

    $items = OrderItem::factory()->count(2)->state([
        'quantity' => 2,
        'price' => 15000,
        'product_offer_id' => $productOffer->id,
    ]);

    $this->order = Order::factory()
        ->for($this->clinic, 'clinic')
        ->has($items, 'items')
        ->create();

    $this->suborder = SubOrder::factory()->state([
        'order_id' => $this->order->id,
        'vendor_id' => $productOffer->vendor_id,
    ])->create();

    $this->externalOrder = ExternalOrder::factory()->create([
        'sub_order_id' => $this->suborder->id,
        'shipping_fee' => 1100,
    ]);
});

test('api contract', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/orders/{$this->order->id}");

    $response->assertOk()
        ->assertJsonStructure([
            'id',
            'orderNumber',
            'totalPrice',
            'date',
            'status',
            'downloadChecklistUrl',
            'downloadInvoicesUrl',
            'hasBackorderedItems',
            'vendorOrders' => [
                '*' => [
                    'items' => [
                        '*' => [
                            'id',
                            'orderNumber',
                            'unitPrice',
                            'quantity',
                            'totalPrice',
                            'status',
                            'productOfferId',
                            'product' => [
                                'id',
                                'name',
                                'imageUrl',
                            ],

                        ],
                    ],
                    'vendor' => [
                        'id',
                        'name',
                        'imageUrl',
                    ],
                    'totalPrice',
                    'totalTaxFee',
                    'shippingFee',
                ],
            ],
            'promotions' => [
                '*' => [
                    'id',
                    'name',
                    'type',
                    'triggering_items',
                    'applied_rules',
                    'applied_benefits',
                ],
            ],
        ]);
});

test('authentication', function () {
    $this->getJson("/api/orders/{$this->order->id}")->assertUnauthorized();
});

test('limit access to user clinic', function () {
    $account = ClinicAccount::factory()->create();

    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->for($account, 'account')->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->withHeader('Highfive-Clinic', $clinic->id)
        ->getJson("/api/orders/{$this->order->id}");

    $response->assertForbidden();
});

test('require highfive-clinic header', function () {
    $this->actingAs($this->user)
        ->getJson("/api/orders/{$this->order->id}")
        ->assertBadRequest()
        ->assertJson([
            'message' => 'The highfive-clinic header is required.',
        ]);
});

test('has correct values', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/orders/{$this->order->id}");

    $response->assertOk()
        ->assertJsonFragment([
            'unitPrice' => Money::ofMinor(OrderItem::first()->price ?? 0, 'USD')->getAmount(),
            'totalPrice' => Money::ofMinor(OrderItem::all()->sum('total_price'), 'USD')->getAmount(),
            'totalTaxFee' => Money::ofMinor(OrderItem::all()->sum('tax_fee'), 'USD')->getAmount(),
            'shippingFee' => Money::ofMinor($this->externalOrder->shipping_fee, 'USD')->getAmount(),
        ]);
});

test('includes promotions when present', function () {
    // Create a promotion and order promotion
    $promotion = App\Modules\Promotion\Models\Promotion::factory()->create([
        'name' => 'Test Promotion',
        'type' => App\Modules\Promotion\Enums\PromotionType::BuyXGetY,
    ]);

    $orderPromotion = App\Models\OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => 'test-id',
                'product_id' => 'test-product-id',
                'quantity' => 5,
            ],
        ],
        'applied_benefits' => [
            [
                'type' => App\Modules\Promotion\Enums\ActionType::GiveFreeProduct->value,
                'product_offer_id' => 'test-product-offer-id',
                'quantity' => 1,
            ],
        ],
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/orders/{$this->order->id}");

    $response->assertOk()
        ->assertJsonFragment([
            'id' => $orderPromotion->id,
            'name' => 'Test Promotion',
        ]);
});
